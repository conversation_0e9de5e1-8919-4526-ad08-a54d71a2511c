# Decoded Extension Directory Structure

## Overview
This directory contains the fully decoded and documented version of the malicious AdawaTools Chrome extension. All obfuscation has been removed and the code has been clearly commented to show its malicious functionality.

## Directory Structure

```
Decoded_Extension/
├── README.md                           # Main documentation and warning
├── SECURITY_ANALYSIS.md               # Comprehensive security analysis
├── DIRECTORY_STRUCTURE.md             # This file
├── manifest.json                      # Decoded manifest file
│
├── background/                        # Background scripts
│   ├── service_worker.js             # Main background service worker
│   └── extension_monitor.js          # Extension detection and removal
│
├── content/                          # Content scripts
│   ├── auth_injector.js             # Cookie injection system
│   ├── global/                      # Global manipulation scripts
│   │   ├── ui_cleaner.js           # UI element removal
│   │   ├── request_interceptor.js  # Request blocking
│   │   └── extension_detector.js   # Client-side extension detection
│   └── sites/                       # Site-specific manipulation
│       ├── ahrefs_manipulator.js   # Ahrefs UI manipulation
│       ├── canva_manipulator.js    # Canva UI manipulation
│       ├── semrush_manipulator.js  # SEMrush UI manipulation
│       ├── grammarly_manipulator.js # Grammarly UI manipulation
│       ├── skillshare_manipulator.js # Skillshare UI manipulation
│       ├── netflix_manipulator.js  # Netflix UI manipulation
│       └── [20+ other site scripts] # Additional site manipulators
│
├── popup/                            # Extension popup interface
│   ├── popup.html                   # Popup HTML interface
│   └── popup.js                     # Popup functionality
│
├── rules/                            # Declarative net request rules
│   └── blocking_rules.json         # Request blocking rules
│
├── icons/                            # Extension icons
│   └── icon128.png                  # Extension icon
│
├── lib/                              # Third-party libraries
│   └── jquery.min.js               # jQuery library
│
└── options/                          # Extension options page
    ├── options.html                 # Options page HTML
    └── options.js                   # Options page functionality
```

## File Descriptions

### Core Files

**manifest.json**
- Decoded Chrome extension manifest
- Shows all permissions and content script configurations
- Reveals the true scope of the extension's access

**README.md**
- Main documentation with warnings about the extension's illegal nature
- Overview of functionality and targeted services
- Legal and ethical implications

**SECURITY_ANALYSIS.md**
- Comprehensive technical analysis
- Attack vector documentation
- Mitigation strategies and detection methods

### Background Scripts

**background/service_worker.js**
- Main background service worker (Manifest V3)
- Handles cookie injection and request modification
- Contains the core malicious functionality

**background/extension_monitor.js**
- Monitors for competing extensions
- Automatically removes cookie management tools
- Implements anti-detection measures

### Content Scripts

**content/auth_injector.js**
- Injects premium session cookies
- Monitors for authentication pages
- Bypasses premium content restrictions

**content/sites/[service]_manipulator.js**
- Site-specific UI manipulation scripts
- Removes logout buttons and billing pages
- Hides subscription status indicators

### User Interface

**popup/popup.html & popup.js**
- Extension popup interface
- Shows fake "premium access" status
- Provides controls for cookie management

### Request Blocking

**rules/blocking_rules.json**
- Declarative net request rules
- Blocks access to logout and billing pages
- Maintains persistent unauthorized sessions

## Original vs Decoded

### Original Extension Structure
```
Extension 1/
├── manifest.json (Unicode encoded)
├── popup.html
├── Extension Files/
│   ├── ext_bone/ (Obfuscated core files)
│   │   ├── background.js (Hex encoded)
│   │   ├── script-wo.js (Heavily obfuscated)
│   │   └── [other obfuscated files]
│   ├── codes/ (Site-specific obfuscated scripts)
│   │   ├── code-c.js (Canva - hex encoded)
│   │   ├── code-sr.js (SEMrush - obfuscated)
│   │   └── [20+ other encoded files]
│   └── scripts/ (Content injection scripts)
│       ├── x46x49.js (Ahrefs - obfuscated)
│       └── [15+ other hex-named files]
```

### Decoded Extension Structure
```
Decoded_Extension/
├── manifest.json (Human readable)
├── README.md (Comprehensive documentation)
├── SECURITY_ANALYSIS.md (Technical analysis)
├── background/ (Clearly organized)
├── content/ (Logically structured)
├── popup/ (Clean interface)
└── rules/ (Documented blocking rules)
```

## Key Improvements in Decoded Version

1. **Clear Documentation:** Every file is thoroughly documented
2. **Logical Organization:** Files are organized by functionality
3. **Readable Code:** All obfuscation removed, clear variable names
4. **Security Analysis:** Comprehensive threat analysis included
5. **Legal Warnings:** Clear warnings about illegal nature
6. **Educational Value:** Can be used for security research and training

## Usage Warning

⚠️ **CRITICAL WARNING** ⚠️

This decoded extension is provided for:
- Security research and analysis
- Educational purposes
- Malware detection development
- Legal evidence collection

**DO NOT:**
- Install or use this extension
- Distribute for malicious purposes
- Use to bypass authentication systems
- Violate terms of service of any platform

The use of this extension for unauthorized access is illegal and unethical. This decoded version is provided to help security professionals understand and defend against such threats.

## Legal Notice

This decoded extension is provided under the principle of security research and education. The original extension violates:

- Computer Fraud and Abuse Act (CFAA)
- Digital Millennium Copyright Act (DMCA)
- Terms of Service of multiple platforms
- International computer crime laws

Any use of this code for illegal purposes is strictly prohibited and may result in criminal prosecution.

## Contact Information

If you discover this extension in the wild or need assistance with detection/removal:

1. Report to Chrome Web Store abuse team
2. Contact affected service providers
3. Report to appropriate law enforcement
4. Share threat intelligence with security community

Remember: The goal of this decoded version is to help defend against such threats, not to enable them.
