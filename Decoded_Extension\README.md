# AdawaTools Chrome Extension - Decoded Analysis

## ⚠️ WARNING: MALICIOUS EXTENSION ⚠️

This Chrome extension is designed to bypass premium service authentication by injecting cookies and manipulating website interfaces. **This is illegal and violates terms of service of multiple platforms.**

## What This Extension Does

### 1. <PERSON>ie Injection
- Injects session cookies for premium services to bypass authentication
- Targets major platforms like:
  - Ahrefs (SEO tools)
  - SEMrush (SEO tools) 
  - <PERSON><PERSON> (writing assistant)
  - Canva (design platform)
  - Skillshare (learning platform)
  - LinkedIn Learning
  - Netflix
  - And 20+ other premium services

### 2. UI Manipulation
- Removes logout buttons and billing pages
- Hides subscription status indicators
- Removes upgrade prompts and payment forms
- Blocks access to account settings

### 3. Extension Detection & Removal
- Detects other cookie management extensions
- Automatically uninstalls competing extensions
- Monitors for installation of cookie-related tools
- Clears cookies when threats are detected

### 4. Request Modification
- Changes User-Agent headers for specific sites
- Blocks requests to logout and billing endpoints
- Modifies web requests to maintain session persistence

## Targeted Services

The extension specifically targets these premium services:

**SEO & Marketing Tools:**
- Ahrefs
- SEMrush  
- Moz
- SpyFu
- BuzzSumo
- Alexa
- Serpstat

**Design & Creative:**
- Canva
- Crello
- Envato Elements
- StockUnlimited
- Placeit
- PicMonkey

**Learning Platforms:**
- Skillshare
- LinkedIn Learning
- Lynda

**Writing & Productivity:**
- Grammarly
- WordAI
- Quetext

**Video & Animation:**
- Animoto
- Magisto
- Storyblocks
- Netflix

## Technical Implementation

### Manifest V3 Structure
- Uses service worker for background processing
- Implements declarativeNetRequest for request blocking
- Content scripts for UI manipulation
- Extensive permissions for system access

### Obfuscation Techniques
- Heavy use of hex encoding
- Variable name obfuscation
- String array manipulation
- Function name scrambling

### Anti-Detection Measures
- Monitors for cookie management extensions
- Automatic cleanup of competing tools
- Session persistence mechanisms
- Request header spoofing

## Legal and Ethical Issues

1. **Terms of Service Violation**: Bypasses authentication systems
2. **Copyright Infringement**: Unauthorized access to premium content
3. **Computer Fraud**: Manipulates web applications without authorization
4. **Privacy Violation**: Monitors and manipulates user browser data

## Files Structure

```
Extension/
├── manifest.json (Manifest V3 configuration)
├── popup.html (Extension popup interface)
├── Extension Files/
│   ├── ext_bone/ (Core functionality)
│   │   ├── background.js (Service worker)
│   │   ├── script-wo.js (Extension detection)
│   │   ├── cookies.js (Cookie management)
│   │   └── active.js (Activity monitoring)
│   ├── codes/ (Site-specific scripts)
│   │   ├── code-c.js (Canva manipulation)
│   │   ├── code-sr.js (SEMrush manipulation)
│   │   └── [20+ other site scripts]
│   └── scripts/ (Content injection)
│       ├── x46x49.js (Ahrefs manipulation)
│       └── [15+ other injection scripts]
```

## Recommendations

1. **Do not use this extension** - it's illegal and unethical
2. **Report to authorities** if you encounter this in the wild
3. **Use legitimate alternatives** - pay for services you use
4. **Educate users** about the risks of such tools

## Legitimate Alternatives

Instead of using this malicious extension, consider:
- Free tiers of premium services
- Educational discounts for students
- Open-source alternatives
- Trial periods offered by services
- Legitimate group subscriptions

---

**Disclaimer**: This analysis is for educational and security research purposes only. Do not use this extension or similar tools to bypass authentication systems.
