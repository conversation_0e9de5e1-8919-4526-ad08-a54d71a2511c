/**
 * AdawaTools Chrome Extension - Background Service Worker
 * 
 * WARNING: This is malicious code that bypasses authentication systems
 * This file is decoded for educational/security research purposes only
 */

// Import additional background scripts
try {
    importScripts(
        "cookie_injector.js",
        "extension_monitor.js", 
        "request_blocker.js",
        "activity_tracker.js"
    );

    // Main cookie injection handler
    chrome.runtime.onMessage.addListener(function(cookieData, sender, sendResponse) {
        try {
            const cookies = JSON.parse(cookieData);
            
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i];
                
                // Construct cookie URL
                cookie.url = "http" + (cookie.secure ? "s" : "") + "://" + 
                           cleanDomain(cookie.domain) + cookie.path;
                
                // Remove problematic properties
                delete cookie.id;
                delete cookie.hostOnly;
                delete cookie.session;
                
                // Remove existing session cookie first
                chrome.cookies.remove({
                    url: cookie.url, 
                    name: "session_buzzsumo"
                });
                
                // Set expiration to future date (1 day from now)
                const currentTime = Math.round(+new Date() / 1000);
                cookie.expirationDate = parseInt(currentTime + 86400); // 24 hours
                
                // Inject the premium session cookie
                chrome.cookies.set(cookie, function(result) {
                    if (chrome.runtime.lastError) {
                        console.error("Cookie injection failed:", chrome.runtime.lastError);
                    } else {
                        console.log("Premium cookie injected successfully");
                    }
                });
            }
            
            sendResponse("done");
            console.log("Developed by Likith"); // Original developer signature
            
        } catch (error) {
            console.error("Cookie injection error:", error);
            sendResponse("error");
        }
    });

    // Browser action click handler - opens AdawaTools website
    chrome.browserAction.onClicked.addListener(function() {
        window.open("https://seotoolbd.com", "_newtab");
    });

    // Helper function to clean domain names
    function cleanDomain(domain) {
        return String(domain).replace(/^\./, "");
    }

    // Request header modification for specific sites
    chrome.webRequest.onBeforeSendHeaders.addListener(
        function(details) {
            // Modify User-Agent for Ahrefs and other targeted sites
            for (let i = 0; i < details.requestHeaders.length; ++i) {
                if (details.requestHeaders[i].name === "User-Agent") {
                    // Spoof User-Agent to avoid detection
                    details.requestHeaders[i].value = 
                        "Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 " +
                        "(KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36";
                    break;
                }
            }
            return {requestHeaders: details.requestHeaders};
        },
        {
            urls: [
                "https://ahrefs.com/*",
                "https://*.ahrefs.com/*"
            ]
        },
        ["blocking", "requestHeaders"]
    );

    // Block logout and billing pages to maintain session
    chrome.webRequest.onBeforeRequest.addListener(
        function(details) {
            return {cancel: true}; // Block the request
        },
        {
            urls: [
                // Ahrefs logout/billing pages
                "*://*.ahrefs.com/user/logout*",
                "*://*.ahrefs.com/account/*",
                "*://*.ahrefs.com/billing-admin/*",
                "*://*.ahrefs.com/billing/*",
                "*://*.ahrefs.com/prices/*",
                
                // SEMrush logout/billing pages  
                "*://*.semrush.com/sso/*",
                "*://*.semrush.com/billing-admin/*",
                "*://*.semrush.com/billing/*",
                "*://*.semrush.com/prices/*",
                "*://*.semrush.com/accounts/*",
                
                // Moz logout/billing pages
                "*://*.moz.com/logout/*",
                "*://*.moz.com/account/*", 
                "*://*.moz.com/subscriptions/*",
                "*://*.moz.com/auth/*",
                "*://*.moz.com/email/*",
                "*://*.moz.com/users/auth/*",
                "*://*.moz.com/community/*",
                "*://*.moz.com/billing/*",
                "*://*.moz.com/checkout/*",
                
                // Alexa logout/billing pages
                "*://*.alexa.com/account/*",
                "*://*.alexa.com/logout*",
                "*://*.alexa.com/pro/subscription*",
                "*://*.alexa.com/password/change*",
                "*://*.alexa.com/email/requestchange*",
                
                // Grammarly logout/billing pages
                "*://*.grammarly.com/profile/*",
                "*://*.grammarly.com/settings/*",
                "*://*.grammarly.com/logout*",
                
                // Canva logout/billing pages
                "*://*.canva.com/account*",
                "*://*.canva.com/logout*",
                
                // LinkedIn logout/billing pages
                "*://*.linkedin.com/psettings/*",
                "*://*.linkedin.com/m/logout/*",
                "*://*.linkedin.com/help/learning*",
                "*://*.linkedin.com/learning/logout*",
                
                // Skillshare logout/billing pages
                "*://*.skillshare.com/profile/*",
                "*://*.skillshare.com/settings/*",
                "*://*.skillshare.com/teams*",
                "*://*.skillshare.com/help*",
                "*://*.skillshare.com/logout*",
                
                // Netflix logout/billing pages
                "*://*.netflix.com/YourAccount*",
                "*://*.netflix.com/SignOut*",
                
                // StockUnlimited logout/billing pages
                "*://*.stockunlimited.com/purchase_plan.php*",
                "*://*.stockunlimited.com/account*",
                "*://*.stockunlimited.com/download_history.php*",
                "*://*.stockunlimited.com/auth_action.php?action=logout*",
                "*://*.stockunlimited.com/preferences.php*",
                
                // Freepik logout/billing pages
                "*://*.freepik.com/profile/my_subscriptions*",
                "*://*.freepik.com/profile/preagreement/getstarted*",
                "*://*.support.freepik.com/*",
                
                // PNGTree logout/billing pages
                "*://*.pngtree.com/login/logout*",
                "*://*.pngtree.com/invite-friends*",
                "*://*.pngtree.com/user/my-profile*",
                "*://*.pngtree.com/user/my-subscriptions*",
                "*://*.pngtree.com/notice*",
                "*://*.upload.pngtree.com/?r=upload*",
                
                // Storyblocks logout/billing pages
                "*://*.support.storyblocks.com/*",
                "*://*.storyblocks.com/member/logout*",
                "*://*.videoblocks.com/member/profile*",
                "*://*.support.audioblocks.com/*",
                "*://*.audioblocks.com/member/logout*",
                
                // Other services logout/billing pages
                "*://*.spyfu.com/account/*",
                "*://*.spyfu.com/auth/logout*",
                "*://*.spinrewriter.com/action/log-out*",
                "*://*.elements.envato.com/sign-out*",
                "*://*.keywordrevealer.com/auth/logout*",
                "*://*.keywordrevealer.com/auth/profile*",
                "*://*.app.buzzsumo.com/settings/*",
                "*://*.woorank.com/en/user/*",
                "*://*.woorank.com/en/logout*",
                "*://*.magisto.com/account*",
                "*://*.seoprofiler.com/account/settings*",
                "*://*.indexification.com/support.php*",
                "*://*.indexification.com/members/integration.php*",
                "*://*.indexification.com/members/api.php*",
                "*://*.indexification.com/members/billing.php*",
                "*://*.indexification.com/members/profile.php*",
                "*://*.articlebuilder.net/?action=logout*",
                "*://*.copywritely.com/account/*",
                "*://*.app.ninjaoutreach.com/Settings*",
                "*://*.app.ninjaoutreach.com/StripeWorkflow*",
                "*://*.renderforest.com/subscription*",
                "*://*.renderforest.com/profile*",
                "*://*.renderforest.com/logout*"
            ]
        },
        ["blocking"]
    );

} catch (error) {
    console.error("Background script initialization failed:", error);
}
