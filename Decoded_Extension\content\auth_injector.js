/**
 * Authentication <PERSON>ie Injector
 * 
 * This script injects premium session cookies for various services
 * WARNING: This is illegal and violates terms of service
 */

// Premium session cookies for various services
// These are likely stolen from legitimate premium accounts
const PREMIUM_COOKIES = {
    'ahrefs.com': [
        {
            name: 'session_id',
            value: 'premium_session_token_here',
            domain: '.ahrefs.com',
            path: '/',
            secure: true,
            httpOnly: false
        }
    ],
    
    'semrush.com': [
        {
            name: 'PHPSESSID',
            value: 'premium_php_session_here',
            domain: '.semrush.com', 
            path: '/',
            secure: true,
            httpOnly: false
        }
    ],
    
    'grammarly.com': [
        {
            name: 'grauth',
            value: 'premium_grammarly_auth_here',
            domain: '.grammarly.com',
            path: '/',
            secure: true,
            httpOnly: false
        }
    ],
    
    'canva.com': [
        {
            name: 'session_token',
            value: 'premium_canva_session_here',
            domain: '.canva.com',
            path: '/',
            secure: true,
            httpOnly: false
        }
    ],
    
    'skillshare.com': [
        {
            name: 'session_id',
            value: 'premium_skillshare_session_here',
            domain: '.skillshare.com',
            path: '/',
            secure: true,
            httpOnly: false
        }
    ],
    
    'netflix.com': [
        {
            name: 'NetflixId',
            value: 'premium_netflix_id_here',
            domain: '.netflix.com',
            path: '/',
            secure: true,
            httpOnly: false
        }
    ]
};

// Function to inject cookies for current domain
function injectPremiumCookies() {
    const currentDomain = window.location.hostname.replace('www.', '');
    
    // Check if we have premium cookies for this domain
    Object.keys(PREMIUM_COOKIES).forEach(domain => {
        if (currentDomain.includes(domain)) {
            const cookies = PREMIUM_COOKIES[domain];
            
            // Send cookies to background script for injection
            chrome.runtime.sendMessage(JSON.stringify(cookies), function(response) {
                if (response === 'done') {
                    console.log(`Premium cookies injected for ${domain}`);
                    
                    // Reload page to apply new session
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                }
            });
        }
    });
}

// Function to monitor for login/authentication pages
function monitorAuthPages() {
    const authKeywords = [
        'login', 'signin', 'auth', 'subscribe', 'upgrade', 'premium', 'pro'
    ];
    
    const currentUrl = window.location.href.toLowerCase();
    const hasAuthKeyword = authKeywords.some(keyword => currentUrl.includes(keyword));
    
    if (hasAuthKeyword) {
        // Inject cookies when on authentication-related pages
        setTimeout(injectPremiumCookies, 2000);
    }
}

// Function to bypass premium content restrictions
function bypassPremiumRestrictions() {
    // Remove common premium restriction overlays
    const restrictionSelectors = [
        '.premium-overlay',
        '.subscription-required',
        '.paywall',
        '.upgrade-prompt',
        '.pro-only',
        '.premium-content-lock'
    ];
    
    restrictionSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => element.remove());
    });
    
    // Remove blur effects commonly used for premium content
    const blurredElements = document.querySelectorAll('[style*="blur"]');
    blurredElements.forEach(element => {
        element.style.filter = 'none';
        element.style.webkitFilter = 'none';
    });
}

// Execute on page load
document.addEventListener('DOMContentLoaded', function() {
    monitorAuthPages();
    bypassPremiumRestrictions();
    
    // Inject cookies for current domain
    setTimeout(injectPremiumCookies, 3000);
});

// Monitor for page changes (SPA navigation)
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        monitorAuthPages();
        bypassPremiumRestrictions();
    }
}).observe(document, {subtree: true, childList: true});

// Periodic execution to handle dynamic content
setInterval(() => {
    bypassPremiumRestrictions();
}, 5000);

/**
 * Cookie Injection Strategy:
 * 
 * 1. Monitors for authentication and premium pages
 * 2. Injects stolen premium session cookies
 * 3. Bypasses premium content restrictions
 * 4. Removes paywall overlays and blur effects
 * 5. Reloads pages to apply new authentication state
 * 
 * This allows unauthorized access to premium content by:
 * - Using stolen session cookies from legitimate premium accounts
 * - Bypassing client-side premium restrictions
 * - Maintaining persistent premium access across sessions
 */
