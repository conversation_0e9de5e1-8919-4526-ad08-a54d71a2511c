/**
 * Ahrefs Website Manipulation Script
 * 
 * This script removes UI elements that would reveal the user doesn't have a premium account
 * WARNING: This is used to deceive users about their subscription status
 */

// Common UI manipulation variables (decoded from obfuscated arrays)
const UI_ELEMENTS_TO_REMOVE = [
    'a.btn-subscribe',           // Subscribe buttons
    'span.icon--profile',        // Profile icons that show account status
    'a.hover-danger',            // Logout/danger buttons
    '.css-1u5msoo-alertWithBorder', // Alert banners about subscription
    '#userMenuDropdown',         // User menu dropdown
    '.LandingTabs',             // Landing page tabs
    '.css-ru5trt-entryCounter'  // Entry counters that show usage limits
];

// Function to hide premium/subscription related elements
function hideAhrefsElements() {
    // Change page title to hide Ahrefs branding
    document.title = "Ahrefs" + " - Premium Access";
    
    // Remove all subscription-related UI elements
    UI_ELEMENTS_TO_REMOVE.forEach(selector => {
        $(selector).remove();
    });
    
    // Remove additional elements that might show account limitations
    $('.landingTabs').remove();
    $('.css-a6fcgv-alert').remove();
    
    console.log("Ahrefs UI manipulation completed");
}

// Execute manipulation when page loads
$(document).ready(function() {
    hideAhrefsElements();
    
    // Re-run manipulation periodically in case of dynamic content
    setInterval(hideAhrefsElements, 2000);
});

// Also run on any DOM changes
const observer = new MutationObserver(function(mutations) {
    hideAhrefsElements();
});

observer.observe(document.body, {
    childList: true,
    subtree: true
});

/**
 * What this script does:
 * 
 * 1. Removes subscription buttons and upgrade prompts
 * 2. Hides user profile elements that show account status
 * 3. Removes logout buttons to prevent session termination
 * 4. Hides usage counters and limits
 * 5. Removes alert banners about subscription requirements
 * 6. Changes page title to suggest premium access
 * 
 * This creates a false impression that the user has premium access
 * when they may only have injected session cookies from another account.
 */
