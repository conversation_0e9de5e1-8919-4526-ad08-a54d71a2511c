/**
 * Canva Website Manipulation Script
 * 
 * Removes premium upgrade prompts and subscription-related UI elements
 * WARNING: This deceives users about their actual subscription status
 */

// Decoded selectors for Canva UI elements to remove
const CANVA_ELEMENTS_TO_REMOVE = [
    ".ok2g_v2i6KAtSX08ySuv7",      // Premium upgrade prompts
    ".pvf_dg.mRLVxw",             // Subscription banners
    ".R3lH9w.mRLVxw",             // Account status indicators
    "._1cYWFA",                   // Premium feature locks
    "a[href=\"/learn/design-school\"]", // Design school links (premium)
    "a[href=\"/teams\"]",         // Teams feature links (premium)
    "._2wrQ3QQxMpZu105bjNfRim",   // Premium template indicators
    "._2k57_GeORGeXMRz2wk-qNs",   // Pro badge elements
    "._23f99zyGZtlDWreJ5dNkE",    // Subscription reminder elements
    "._2bpGdX5wP_sxV1BblwpVu8"   // Premium feature callouts
];

// Function to remove Canva premium elements
function removeCanvaElements() {
    CANVA_ELEMENTS_TO_REMOVE.forEach(selector => {
        $(selector).remove();
    });
    
    console.log("Canva premium elements removed");
}

// Execute on page load
$(document).ready(function() {
    removeCanvaElements();
});

// Re-run on mouse hover to catch dynamically loaded content
$('body').on('mouseover', function() {
    removeCanvaElements();
});

// Continuous monitoring for dynamic content
setInterval(removeCanvaElements, 3000);

/**
 * Canva Manipulation Strategy:
 * 
 * 1. Removes "Pro" badges and premium indicators
 * 2. Hides upgrade prompts and subscription banners
 * 3. Removes premium feature locks and paywalls
 * 4. Hides team collaboration features (premium only)
 * 5. Removes design school links (premium feature)
 * 6. Hides subscription reminder elements
 * 
 * This makes it appear as if the user has Canva Pro access
 * when they may only have stolen session cookies.
 */
