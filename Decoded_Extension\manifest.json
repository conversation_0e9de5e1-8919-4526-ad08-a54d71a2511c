{"manifest_version": 3, "name": "AdawaTools - 1", "version": "1.0", "author": "AdawaTools", "description": "This extension has been developed by AdawaTools and will work on any chromium based browser.", "omnibox": {"keyword": "EVO"}, "permissions": ["declarativeNetRequest", "tabs", "proxy", "cookies", "storage", "management", "clipboardWrite", "clipboardRead", "webRequest", "activeTab", "declarativeNetRequestWithHostAccess", "declarativeNetRequestFeedback"], "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "rules/blocking_rules.json"}]}, "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "background": {"service_worker": "background/service_worker.js"}, "options_page": "options/options.html", "action": {"default_popup": "popup/popup.html"}, "homepage_url": "https://adawatools.com/", "icons": {"128": "icons/icon128.png"}, "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["content/auth_injector.js", "lib/jquery.min.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://ahrefs.com/*"], "js": ["content/sites/ahrefs_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.semrush.com/*"], "js": ["content/sites/semrush_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.moz.com/*"], "js": ["content/sites/moz_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.woorank.com/*"], "js": ["content/sites/woorank_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.canva.com/*"], "js": ["content/sites/canva_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.grammarly.com/*"], "js": ["content/sites/grammarly_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.wordai.com/*"], "js": ["content/sites/wordai_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.storybase.com/*"], "js": ["content/sites/storybase_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.linkedin.com/*"], "js": ["content/sites/linkedin_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.crazyegg.com/*"], "js": ["content/sites/crazyegg_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.buzzsumo.com/*"], "js": ["content/sites/buzzsumo_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.amztracker.com/*"], "js": ["content/sites/amztracker_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.articlebuilder.net/*"], "js": ["content/sites/articlebuilder_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.storyblocks.com/*"], "js": ["lib/jquery.min.js", "content/sites/storyblocks_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.motionarray.com/*"], "js": ["lib/jquery.min.js", "content/sites/motionarray_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.monsterone.com/*"], "js": ["lib/jquery.min.js", "content/sites/monsterone_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.stockrush.io/*"], "js": ["lib/jquery.min.js", "content/sites/stockrush_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.elements.envato.com/*"], "js": ["lib/jquery.min.js", "content/sites/envato_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.tutsplus.com/*"], "js": ["lib/jquery.min.js", "content/sites/tutsplus_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.primevideo.com/*"], "js": ["lib/jquery.min.js", "content/sites/primevideo_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["<all_urls>"], "js": ["content/global/ui_cleaner.js", "content/global/request_interceptor.js", "content/global/extension_detector.js"], "run_at": "document_end"}, {"matches": ["*://*.netflix.com/*"], "js": ["content/sites/netflix_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.canva.com/*"], "js": ["content/sites/canva_advanced.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.stockunlimited.com/*"], "js": ["content/sites/stockunlimited_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.crello.com/*"], "js": ["content/sites/crello_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.app.kwfinder.com/*"], "js": ["content/sites/kwfinder_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.picmonkey.com/*"], "js": ["content/sites/picmonkey_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.placeit.net/*"], "js": ["content/sites/placeit_manipulator.js"], "run_at": "document_end", "all_frames": true}, {"matches": ["*://*.skillshare.com/*"], "js": ["content/sites/skillshare_manipulator.js"], "run_at": "document_end", "all_frames": true}]}