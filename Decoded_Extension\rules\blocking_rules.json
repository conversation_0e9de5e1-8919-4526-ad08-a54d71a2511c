[{"id": 1, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/logout*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 2, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/billing*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 3, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/account*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 4, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/subscription*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 5, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/settings*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 6, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/profile*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 7, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/upgrade*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 8, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/pricing*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 9, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/auth*", "resourceTypes": ["main_frame", "sub_frame"]}}, {"id": 10, "priority": 1, "action": {"type": "block"}, "condition": {"urlFilter": "*://*/signin*", "resourceTypes": ["main_frame", "sub_frame"]}}]