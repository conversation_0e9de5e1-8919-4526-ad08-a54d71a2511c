async function alert({
html,
title = chrome.runtime.getManifest().name,
width = 700,
height = 150,
left,
top,
}) {
const w = left == null && top == null && await chrome.windows.getCurrent();
const w2 = await chrome.windows.create({
  url: `data:text/html,<title>${title}</title><body style="background-color: black;"></body>${html}`.replace(/#/g, '%23'),
  type: 'popup',
  left: left ?? Math.floor(w.left + (w.width - width) / 2),
  top: top ?? Math.floor(w.top + (w.height - height) / 2),
  height,
  width,
});
return new Promise(resolve => {
  chrome.windows.onRemoved.addListener(onRemoved, {windowTypes: ['popup']});
  function onRemoved(id) {
    if (id === w2.id) {
      chrome.windows.onRemoved.removeListener(onRemoved);
      resolve();
    }
  }
});
}