var _0x17f6=['_self','data','source','text','textContent','none','type','x41x43','x41x55','x56x45','x45x56','message','block','open','addEventListener','sendMessage','getElementById','display'];(function(_0x510609,_0x17f635){var _0x100a1f=function(_0x21d6dc){while(--_0x21d6dc){_0x510609['push'](_0x510609['shift']());}};_0x100a1f(++_0x17f635);}(_0x17f6,0x1b8));var _0x100a=function(_0x510609,_0x17f635){_0x510609=_0x510609-0x0;var _0x100a1f=_0x17f6[_0x510609];return _0x100a1f;};window[_0x100a('0x6')](_0x100a('0x3'),function(_0x2b71ce){if(_0x2b71ce[_0x100a('0xc')]!=window)return;_0x2b71ce[_0x100a('0xb')][_0x100a('0x10')]&&_0x2b71ce[_0x100a('0xb')][_0x100a('0x10')]==_0x100a('0x0')&&chrome['runtime'][_0x100a('0x7')](_0x2b71ce['data'][_0x100a('0xd')],function(_0x5cd980){x41x43=document[_0x100a('0x8')](_0x100a('0x11'))[_0x100a('0xe')],window[_0x100a('0x5')](x41x43,_0x100a('0xa'));});});document[_0x100a('0x8')](_0x100a('0x1'))&&document[_0x100a('0x8')](_0x100a('0x2'))&&(document[_0x100a('0x8')](_0x100a('0x1'))['style']['display']=_0x100a('0xf'),document[_0x100a('0x8')](_0x100a('0x2'))['style'][_0x100a('0x9')]=_0x100a('0x4'));