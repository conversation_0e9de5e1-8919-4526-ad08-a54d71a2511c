{"manifest_version": 3, "name": "AdawaTools - 1", "version": "1.0", "author": "AdawaTools", "description": "This extension has been developed by AdawaTools and will work on any chromium based browser.", "omnibox": {"keyword": "EVO"}, "permissions": ["declarativeNetRequest", "tabs", "proxy", "cookies", "storage", "management", "clipboardWrite", "clipboardRead", "webRequest", "activeTab", "declarativeNetRequestWithHostAccess", "declarativeNetRequestFeedback"], "declarative_net_request": {"rule_resources": [{"id": "ruleset_1", "enabled": true, "path": "Extension Files/ext_bone/set.json"}]}, "host_permissions": ["<all_urls>", "http://*/*", "https://*/*"], "background": {"service_worker": "Extension Files/ext_bone/background.js"}, "options_page": "Extension Files/ext_bone/options.html", "action": {"default_popup": "popup.html"}, "homepage_url": "https://adawatools.com/", "icons": {"128": "Extension Files/icons/128.png"}, "content_scripts": [{"matches": ["http://*/*", "https://*/*"], "js": ["Extension Files/ext_bone/authtoken.js", "Extension Files/ext_bone/jquery.js"], "run_at": "document_end", "all_frames": true}, {"all_frames": true, "js": ["Extension Files/scripts/x46x49.js"], "matches": ["*://ahrefs.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x47.js"], "matches": ["*://*.semrush.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x48.js"], "matches": ["*://*.moz.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x50.js"], "matches": ["*://*.woorank.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x51.js"], "matches": ["*://*.canva.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x52.js"], "matches": ["*://*.grammarly.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x53.js"], "matches": ["*://*.wordai.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x54.js"], "matches": ["*://*.storybase.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x55.js"], "matches": ["*://*.linkedin.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x56.js"], "matches": ["*://*.crazyegg.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x57.js"], "matches": ["*://*.buzzsumo.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x58.js"], "matches": ["*://*.amztracker.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/scripts/x46x59.js"], "matches": ["*://*.articlebuilder.net/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/ext_bone/jquery.js", "Extension Files/codes/sb.js"], "matches": ["*://*.storyblocks.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/ext_bone/jquery.js", "Extension Files/codes/ma.js"], "matches": ["*://*.motionarray.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/ext_bone/jquery.js", "Extension Files/codes/mo.js"], "matches": ["*://*.monsterone.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/ext_bone/jquery.js", "Extension Files/codes/sr.js"], "matches": ["*://*.stockrush.io/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/ext_bone/jquery.js", "Extension Files/codes/ee.js"], "matches": ["*://*.elements.envato.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/ext_bone/jquery.js", "Extension Files/codes/et.js"], "matches": ["*://*.tutsplus.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["Extension Files/ext_bone/jquery.js", "Extension Files/codes/code-pr.js"], "matches": ["*://*.primevideo.com/*"], "run_at": "document_end"}, {"matches": ["<all_urls>"], "js": ["Extension Files/scripts/x46x45.js", "Extension Files/scripts/x46x46.js", "Extension Files/scripts/x46x44.js"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-n.js"], "matches": ["*://*.netflix.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-c.js"], "matches": ["*://*.canva.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-su.js"], "matches": ["*://*.stockunlimited.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-n.js"], "matches": ["*://*.netflix.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-cre.js"], "matches": ["*://*.crello.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-kw.js"], "matches": ["*://*.app.kwfinder.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-env.js"], "matches": ["*://*.elements.envato.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-ld.js"], "matches": ["*://*.linkedin.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-mz.js"], "matches": ["*://*.moz.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-pmo.js"], "matches": ["*://*.picmonkey.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-plc.js"], "matches": ["*://*.placeit.net/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-skl.js"], "matches": ["*://*.skillshare.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-sr.js"], "matches": ["*://*.semrush.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-pr.js"], "matches": ["*://*.primevideo.com/*"], "run_at": "document_end"}, {"all_frames": true, "js": ["/Extension Files/codes/code-cop.js"], "matches": ["*://*.grammarly.com/*"], "run_at": "document_end"}]}